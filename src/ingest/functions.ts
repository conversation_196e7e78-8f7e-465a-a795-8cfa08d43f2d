/**
 * Comprehensive Stripe Test Data Ingestion Script (v3)
 *
 * This script is designed to populate a Stripe test account with realistic,
 * time-based subscription data for backfilling and analytics testing.
 *
 * Key Features:
 * - SAFETY CHECKS: Prevents execution on live or production environments.
 * - Cleanup: Deletes existing test data to ensure a clean slate.
 * - Test Clocks: Uses Stripe Test Clocks to simulate the passage of time
 * for accurate event generation (invoices, subscriptions, webhooks).
 * - Historical Simulation: Starts the simulation at the test clock's
 * frozen time, allowing for the creation of historical data.
 * - Lifecycle Events: Simulates customer sign-ups, churn, upgrades,
 * downgrades, and refunds over time.
 * - Pagination: Correctly handles pagination when cleaning up existing
 * resources to ensure all data is removed.
 *
 * Usage:
 * - Ensure your environment variables are set: STRIPE_SECRET_KEY, etc.
 * - Run the script with an Inngest event: `stripe/populate.test.data`
 *
 * Example Event Data:
 * {
 * "testClockId": "clock_1Oq4p4RjW2XmC61y0Zk8j3zF",
 * "initialNumberOfCustomers": 50,
 * "orgIntegrationId": "your_integration_id"
 * }
 */

import Stripe from 'stripe';
import { inngest } from './client';

export const helloWorld = inngest.createFunction(
  { id: 'hello-world' },
  { event: 'test/hello.world' },
  async ({ event, step }) => {
    await step.sleep('wait-a-moment', '1s');
    return { message: `Hello ${event.data.email}!` };
  }
);

interface IngestTestDataParams {
  testClockId: string;
  initialNumberOfCustomers: number;
  orgIntegrationId: string;
}

export const populateStripeTestData = inngest.createFunction(
  { id: 'ingest-test-data' },
  { event: 'stripe/populate.test.data' },
  async ({ event }) => {
    const { testClockId, initialNumberOfCustomers, orgIntegrationId } =
      event.data as IngestTestDataParams;

    // Get and validate integration
    // const integration = await getIntegrationById(orgIntegrationId);
    // if (!integration) {
    //   throw new Error(`Integration with ID ${orgIntegrationId} not found`);
    // }

    // SAFETY CHECK: Ensure this is NOT a live integration
    // if (integration.settings?.livemode === true) {
    //   throw new Error(
    //     'SAFETY ERROR: Cannot populate test data on a live Stripe integration!'
    //   );
    // }

    // if (integration.environment === 'production') {
    //   throw new Error(
    //     'SAFETY ERROR: Cannot populate test data on a production environment integration!'
    //   );
    // }

    // Initialize Stripe client
    // Use main org integration for now
    // const accessToken = getAccessTokenFromIntegration(integration);
    // if (!accessToken) {
    //   throw new Error('No access token found for integration');
    // }

    // process.env.STRIPE_SECRET_KEY
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-08-27.basil',
    });

    // --- Cleanup: Delete existing test data with pagination ---
    console.log('Cleaning up existing test data...');

    // Helper function for paginated deletion
    const deleteAllWithPagination = async (
      listFunction: any,
      deleteFunction: any
    ) => {
      let hasMore = true;
      let lastId: string | null = null;
      while (hasMore) {
        const listParams: any = {
          limit: 100,
          starting_after: lastId || undefined,
        };
        const listResult: any = await listFunction(listParams);

        if (listResult.data.length === 0) {
          hasMore = false;
          continue;
        }

        for (const item of listResult.data) {
          try {
            await deleteFunction(item.id);
          } catch (e: any) {
            console.warn(`Could not delete item ${item.id}: ${e.message}`);
          }
        }
        lastId = listResult.data[listResult.data.length - 1].id;
        hasMore = listResult.has_more;
      }
    };

    // Delete existing subscriptions
    await deleteAllWithPagination(
      (params: any) => stripe.subscriptions.list(params),
      (id: string) => stripe.subscriptions.cancel(id)
    );

    // Delete existing customers
    await deleteAllWithPagination(
      (params: any) => stripe.customers.list(params),
      (id: string) => stripe.customers.del(id)
    );

    // Delete existing products
    let hasMoreProducts = true;
    let lastProductId: string | null = null;
    while (hasMoreProducts) {
      const productsList: any = await stripe.products.list({
        limit: 100,
        starting_after: lastProductId || undefined,
      });
      for (const product of productsList.data) {
        await stripe.products.update(product.id, { active: false });
      }
      lastProductId = productsList.data[productsList.data.length - 1]?.id;
      hasMoreProducts = productsList.has_more;
    }

    // --- Get test clock ---
    const testClock = await stripe.testHelpers.testClocks.retrieve(testClockId);

    // --- Create products and prices ---
    console.log('Creating products and prices...');

    // Create Basic plan product
    const basicProduct = await stripe.products.create({
      name: 'Basic Plan',
      description: 'Basic SaaS plan with essential features',
    });

    // Create Pro plan product
    const proProduct = await stripe.products.create({
      name: 'Pro Plan',
      description: 'Professional SaaS plan with advanced features',
    });

    // Define pricing structure
    const pricingConfig = [
      // Basic plan prices
      { product: basicProduct.id, interval: 'week', amount: 999 }, // $9.99/week
      { product: basicProduct.id, interval: 'month', amount: 2999 }, // $29.99/month
      { product: basicProduct.id, interval: 'year', amount: 29999 }, // $299.99/year
      // Pro plan prices
      { product: proProduct.id, interval: 'week', amount: 1999 }, // $19.99/week
      { product: proProduct.id, interval: 'month', amount: 5999 }, // $59.99/month
      { product: proProduct.id, interval: 'year', amount: 59999 }, // $599.99/year
    ];

    const prices: any[] = [];
    for (const config of pricingConfig) {
      const price = await stripe.prices.create({
        product: config.product,
        unit_amount: config.amount,
        currency: 'usd',
        recurring: {
          interval: config.interval as 'week' | 'month' | 'year',
        },
      });
      prices.push(price);
    }

    const products = { basic: basicProduct, pro: proProduct };

    // --- Create initial customers and subscriptions ---
    console.log('Creating initial customers and subscriptions...');
    const customers = [];
    const subscriptions = [];

    for (let i = 0; i < initialNumberOfCustomers; i++) {
      const customerData = generateCustomerData(i);

      const customer = await stripe.customers.create({
        email: customerData.email,
        name: customerData.name,
        description: `Test customer ${i + 1}`,
      });

      const randomPrice = prices[Math.floor(Math.random() * prices.length)];

      const subscription = await stripe.subscriptions.create({
        customer: customer.id,
        items: [{ price: randomPrice.id }],
        collection_method: 'charge_automatically',
        payment_behavior: 'default_incomplete',
        expand: ['latest_invoice.payment_intent'],
      });

      customers.push(customer);
      subscriptions.push(subscription);
    }

    const initialCustomers = { customers, subscriptions };

    // --- Simulate business activity from test clock time to current time ---
    console.log('Simulating business activity...');
    const results = {
      weeklyStats: [] as Array<{
        week: number;
        startDate: Date;
        endDate: Date;
        newCustomers: number;
        churnedCustomers: number;
        activeSubscriptions: number;
        totalCustomers: number;
        upgrades: number;
        downgrades: number;
        refunds: number;
      }>,
      totalCustomers: initialNumberOfCustomers,
      totalRevenue: 0, // This would require a separate calculation or backfill script
      churnedCustomers: 0,
      upgrades: 0,
      downgrades: 0,
      refunds: 0,
    };

    let currentCustomerCount = initialNumberOfCustomers;
    let activeSubscriptions = [...initialCustomers.subscriptions];

    // Start from test clock's frozen time and simulate until current time
    const startTime = testClock.frozen_time;
    const currentTime = Math.floor(Date.now() / 1000);
    const sevenDays = 7 * 24 * 60 * 60; // 7 days in seconds
    let simulationTime = startTime;
    let weekNumber = 1;

    while (simulationTime < currentTime) {
      const weekStart = simulationTime;
      const weekEnd = Math.min(simulationTime + sevenDays, currentTime);

      // Advance test clock to week start (with retry logic)
      await advanceTestClockWithRetry(stripe, testClockId, weekStart);

      // Calculate time-based growth rate
      const weeksFromStart = (weekStart - startTime) / sevenDays;
      const monthsFromStart = weeksFromStart / 4.33; // approximate weeks per month

      // Growth rate: 20-40% first 6 months, 10-20% after (converted to weekly)
      const monthlyGrowthRate =
        monthsFromStart <= 6
          ? 0.2 + Math.random() * 0.2
          : 0.1 + Math.random() * 0.1;

      const weeklyGrowthRate = monthlyGrowthRate / 4.33;
      const newCustomersThisWeek = Math.floor(
        currentCustomerCount * weeklyGrowthRate
      );

      // Add new customers
      const newCustomers = [];
      for (let i = 0; i < newCustomersThisWeek; i++) {
        const customerData = generateCustomerData(currentCustomerCount + i);
        const customer = await stripe.customers.create({
          email: customerData.email,
          name: customerData.name,
          description: `Week ${weekNumber} customer`,
        });

        const randomPrice = prices[Math.floor(Math.random() * prices.length)];
        const subscription = await stripe.subscriptions.create({
          customer: customer.id,
          items: [{ price: randomPrice.id }],
          collection_method: 'charge_automatically',
        });

        newCustomers.push({ customer, subscription });
        activeSubscriptions.push(subscription as any);
      }
      currentCustomerCount += newCustomersThisWeek;

      // Simulate churn (2-8% monthly, converted to weekly)
      const monthlyChurnRate = 0.02 + Math.random() * 0.06;
      const weeklyChurnRate = monthlyChurnRate / 4.33;
      const churnCount = Math.floor(
        activeSubscriptions.length * weeklyChurnRate
      );
      let weekChurnedCustomers = 0;
      for (let i = 0; i < churnCount; i++) {
        if (activeSubscriptions.length > 0) {
          const randomIndex = Math.floor(
            Math.random() * activeSubscriptions.length
          );
          const subscriptionToCancel = activeSubscriptions[randomIndex];
          await stripe.subscriptions.cancel(subscriptionToCancel.id);
          activeSubscriptions.splice(randomIndex, 1);
          results.churnedCustomers++;
          weekChurnedCustomers++;
        }
      }

      // Simulate upgrades/downgrades (5-10% monthly, converted to weekly)
      const monthlyChangeRate = 0.05 + Math.random() * 0.05;
      const weeklyChangeRate = monthlyChangeRate / 4.33;
      const changeCount = Math.floor(
        activeSubscriptions.length * weeklyChangeRate
      );
      let weekUpgrades = 0;
      let weekDowngrades = 0;
      for (let i = 0; i < changeCount; i++) {
        if (activeSubscriptions.length > 0) {
          const randomIndex = Math.floor(
            Math.random() * activeSubscriptions.length
          );
          const subscriptionToModify = activeSubscriptions[randomIndex];
          const oldPriceId = subscriptionToModify.items.data[0].price.id;
          const newPrice = prices.find(p => p.id !== oldPriceId); // find a different price
          if (!newPrice) continue;
          try {
            await stripe.subscriptions.update(subscriptionToModify.id, {
              items: [
                {
                  id: subscriptionToModify.items.data[0].id,
                  price: newPrice.id,
                },
              ],
              proration_behavior: 'create_prorations',
            });
            const oldAmount =
              subscriptionToModify.items.data[0].price.unit_amount || 0;
            const newAmount = newPrice.unit_amount || 0;
            if (newAmount > oldAmount) {
              results.upgrades++;
              weekUpgrades++;
            } else if (newAmount < oldAmount) {
              results.downgrades++;
              weekDowngrades++;
            }
          } catch (error) {
            console.log(`Failed to modify subscription: ${error}`);
          }
        }
      }

      // Simulate refunds (1-3% of active subscriptions monthly, converted to weekly)
      const monthlyRefundRate = 0.01 + Math.random() * 0.02;
      const weeklyRefundRate = monthlyRefundRate / 4.33;
      const refundCount = Math.floor(
        activeSubscriptions.length * weeklyRefundRate
      );
      let weekRefunds = 0;
      for (let i = 0; i < refundCount; i++) {
        if (activeSubscriptions.length > 0) {
          try {
            const randomActiveSubscription =
              activeSubscriptions[
                Math.floor(Math.random() * activeSubscriptions.length)
              ];
            const charges = await stripe.charges.list({
              customer: randomActiveSubscription.customer as string,
              limit: 10,
            });
            if (charges.data.length > 0) {
              const randomCharge =
                charges.data[Math.floor(Math.random() * charges.data.length)];
              if (randomCharge.paid && !randomCharge.refunded) {
                await stripe.refunds.create({
                  charge: randomCharge.id,
                  amount: Math.floor(
                    randomCharge.amount * (0.5 + Math.random() * 0.5)
                  ),
                });
                results.refunds++;
                weekRefunds++;
              }
            }
          } catch (error) {
            console.log(`Failed to create refund: ${error}`);
          }
        }
      }

      // Advance to end of week (with retry logic)
      await advanceTestClockWithRetry(stripe, testClockId, weekEnd);

      const weekStats = {
        week: weekNumber,
        startDate: new Date(weekStart * 1000),
        endDate: new Date(weekEnd * 1000),
        newCustomers: newCustomersThisWeek,
        churnedCustomers: weekChurnedCustomers,
        activeSubscriptions: activeSubscriptions.length,
        totalCustomers: currentCustomerCount,
        upgrades: weekUpgrades,
        downgrades: weekDowngrades,
        refunds: weekRefunds,
      };
      results.weeklyStats.push(weekStats);

      simulationTime = weekEnd;
      weekNumber++;
    }

    const simulationResults = results;

    return {
      success: true,
      testClockId,
      initialCustomers: initialNumberOfCustomers,
      finalStats: simulationResults,
      products: Object.keys(products),
      pricesCreated: prices.length,
    };
  }
);

// Helper function to generate realistic customer data
function generateCustomerData(index: number) {
  const firstNames = [
    'John',
    'Jane',
    'Mike',
    'Sarah',
    'David',
    'Lisa',
    'Chris',
    'Emma',
    'Alex',
    'Maria',
  ];
  const lastNames = [
    'Smith',
    'Johnson',
    'Williams',
    'Brown',
    'Jones',
    'Garcia',
    'Miller',
    'Davis',
    'Rodriguez',
    'Martinez',
  ];
  const domains = [
    'gmail.com',
    'yahoo.com',
    'hotmail.com',
    'outlook.com',
    'company.com',
  ];

  const firstName = firstNames[index % firstNames.length];
  const lastName =
    lastNames[Math.floor(index / firstNames.length) % lastNames.length];
  const domain = domains[index % domains.length];

  return {
    name: `${firstName} ${lastName}`,
    email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}${index}@${domain}`,
  };
}
