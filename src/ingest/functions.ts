import Stripe from 'stripe';
import {
  getAccessTokenFromIntegration,
  getIntegrationById,
} from '../lib/integrations';
import { inngest } from './client';

export const helloWorld = inngest.createFunction(
  { id: 'hello-world' },
  { event: 'test/hello.world' },
  async ({ event, step }) => {
    await step.sleep('wait-a-moment', '1s');
    return { message: `Hello ${event.data.email}!` };
  }
);

interface IngestTestDataParams {
  testClockId: string;
  initialNumberOfCustomers: number;
  orgIntegrationId: string;
}

export const populateStripeTestData = inngest.createFunction(
  { id: 'ingest-test-data' },
  { event: 'stripe/populate.test.data' },
  async ({ event }) => {
    const { testClockId, initialNumberOfCustomers, orgIntegrationId } =
      event.data as IngestTestDataParams;

    // Get and validate integration
    const integration = await getIntegrationById(orgIntegrationId);
    if (!integration) {
      throw new Error(`Integration with ID ${orgIntegrationId} not found`);
    }

    // SAFETY CHECK: Ensure this is NOT a live integration
    if (integration.settings?.livemode === true) {
      throw new Error(
        'SAFETY ERROR: Cannot populate test data on a live Stripe integration!'
      );
    }

    if (integration.environment === 'production') {
      throw new Error(
        'SAFETY ERROR: Cannot populate test data on a production environment integration!'
      );
    }

    // Initialize Stripe client
    const accessToken = getAccessTokenFromIntegration(integration);
    if (!accessToken) {
      throw new Error('No access token found for integration');
    }

    const stripe = new Stripe(accessToken, {
      apiVersion: '2025-08-27.basil',
    });

    return {};
  }
);
