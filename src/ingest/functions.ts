import Stripe from 'stripe';
import {
  getAccessTokenFromIntegration,
  getIntegrationById,
} from '../lib/integrations';
import { inngest } from './client';

export const helloWorld = inngest.createFunction(
  { id: 'hello-world' },
  { event: 'test/hello.world' },
  async ({ event, step }) => {
    await step.sleep('wait-a-moment', '1s');
    return { message: `Hello ${event.data.email}!` };
  }
);

interface IngestTestDataParams {
  testClockId: string;
  initialNumberOfCustomers: number;
  orgIntegrationId: string;
}

export const populateStripeTestData = inngest.createFunction(
  { id: 'ingest-test-data' },
  { event: 'stripe/populate.test.data' },
  async ({ event, step }) => {
    const { testClockId, initialNumberOfCustomers, orgIntegrationId } =
      event.data as IngestTestDataParams;

    // Get and validate integration
    const integration = await getIntegrationById(orgIntegrationId);
    if (!integration) {
      throw new Error(`Integration with ID ${orgIntegrationId} not found`);
    }

    // SAFETY CHECK: Ensure this is NOT a live integration
    if (integration.settings?.livemode === true) {
      throw new Error(
        'SAFETY ERROR: Cannot populate test data on a live Stripe integration!'
      );
    }

    if (integration.environment === 'production') {
      throw new Error(
        'SAFETY ERROR: Cannot populate test data on a production environment integration!'
      );
    }

    // Initialize Stripe client
    const accessToken = getAccessTokenFromIntegration(integration);
    if (!accessToken) {
      throw new Error('No access token found for integration');
    }

    const stripe = new Stripe(accessToken, {
      apiVersion: '2025-08-27.basil',
    });

    // Step 1: Get test clock and advance to current time
    const testClock = await step.run('get-test-clock', async () => {
      return await stripe.testHelpers.testClocks.retrieve(testClockId);
    });

    const currentTime = Math.floor(Date.now() / 1000);

    await step.run('advance-test-clock', async () => {
      if (testClock.frozen_time < currentTime) {
        await stripe.testHelpers.testClocks.advance(testClockId, {
          frozen_time: currentTime,
        });
      }
      return { advanced_to: currentTime };
    });

    // Step 2: Create products and prices
    const { products, prices } = await step.run(
      'create-products-and-prices',
      async () => {
        // Create Basic plan product
        const basicProduct = await stripe.products.create({
          name: 'Basic Plan',
          description: 'Basic SaaS plan with essential features',
        });

        // Create Pro plan product
        const proProduct = await stripe.products.create({
          name: 'Pro Plan',
          description: 'Professional SaaS plan with advanced features',
        });

        // Define pricing structure
        const pricingConfig = [
          // Basic plan prices
          { product: basicProduct.id, interval: 'week', amount: 999 }, // $9.99/week
          { product: basicProduct.id, interval: 'month', amount: 2999 }, // $29.99/month
          { product: basicProduct.id, interval: 'year', amount: 29999 }, // $299.99/year
          // Pro plan prices
          { product: proProduct.id, interval: 'week', amount: 1999 }, // $19.99/week
          { product: proProduct.id, interval: 'month', amount: 5999 }, // $59.99/month
          { product: proProduct.id, interval: 'year', amount: 59999 }, // $599.99/year
        ];

        const createdPrices = [];
        for (const config of pricingConfig) {
          const price = await stripe.prices.create({
            product: config.product,
            unit_amount: config.amount,
            currency: 'usd',
            recurring: {
              interval: config.interval as 'week' | 'month' | 'year',
            },
          });
          createdPrices.push(price);
        }

        return {
          products: { basic: basicProduct, pro: proProduct },
          prices: createdPrices,
        };
      }
    );

    // Step 3: Create initial customers and subscriptions
    const initialCustomers = await step.run(
      'create-initial-customers',
      async () => {
        const customers = [];
        const subscriptions = [];

        for (let i = 0; i < initialNumberOfCustomers; i++) {
          // Generate realistic customer data
          const customerData = generateCustomerData(i);

          const customer = await stripe.customers.create({
            email: customerData.email,
            name: customerData.name,
            description: `Test customer ${i + 1}`,
          });

          // Randomly assign a plan and billing interval
          const randomPrice = prices[Math.floor(Math.random() * prices.length)];

          const subscription = await stripe.subscriptions.create({
            customer: customer.id,
            items: [{ price: randomPrice.id }],
            collection_method: 'charge_automatically',
            payment_behavior: 'default_incomplete',
            expand: ['latest_invoice.payment_intent'],
          });

          customers.push(customer);
          subscriptions.push(subscription);
        }

        return { customers, subscriptions };
      }
    );

    // Step 4: Simulate 12 months of business activity
    const simulationResults = await step.run(
      'simulate-business-activity',
      async () => {
        const results = {
          monthlyStats: [] as Array<{
            month: number;
            newCustomers: number;
            churnedCustomers: number;
            activeSubscriptions: number;
            totalCustomers: number;
          }>,
          totalCustomers: initialNumberOfCustomers,
          totalRevenue: 0,
          churnedCustomers: 0,
          upgrades: 0,
          downgrades: 0,
          refunds: 0,
        };

        let currentCustomerCount = initialNumberOfCustomers;
        let activeSubscriptions = [...initialCustomers.subscriptions];

        for (let month = 1; month <= 12; month++) {
          const monthStart = currentTime + (month - 1) * 30 * 24 * 60 * 60;
          const monthEnd = currentTime + month * 30 * 24 * 60 * 60;

          // Advance test clock to month start
          await stripe.testHelpers.testClocks.advance(testClockId, {
            frozen_time: monthStart,
          });

          // Calculate growth rate (20-40% first 6 months, 10-20% after)
          const growthRate =
            month <= 6
              ? 0.2 + Math.random() * 0.2 // 20-40%
              : 0.1 + Math.random() * 0.1; // 10-20%

          const newCustomersThisMonth = Math.floor(
            currentCustomerCount * growthRate
          );

          // Add new customers
          const newCustomers = [];
          for (let i = 0; i < newCustomersThisMonth; i++) {
            const customerData = generateCustomerData(currentCustomerCount + i);

            const customer = await stripe.customers.create({
              email: customerData.email,
              name: customerData.name,
              description: `Month ${month} customer`,
            });

            const randomPrice =
              prices[Math.floor(Math.random() * prices.length)];

            const subscription = await stripe.subscriptions.create({
              customer: customer.id,
              items: [{ price: randomPrice.id }],
              collection_method: 'charge_automatically',
            });

            newCustomers.push({ customer, subscription });
            activeSubscriptions.push(subscription as any);
          }

          currentCustomerCount += newCustomersThisMonth;

          // Simulate churn (2-8% monthly)
          const churnRate = 0.02 + Math.random() * 0.06;
          const churnCount = Math.floor(activeSubscriptions.length * churnRate);

          for (let i = 0; i < churnCount; i++) {
            if (activeSubscriptions.length > 0) {
              const randomIndex = Math.floor(
                Math.random() * activeSubscriptions.length
              );
              const subscriptionToCancel = activeSubscriptions[randomIndex];

              await stripe.subscriptions.cancel(subscriptionToCancel.id);
              activeSubscriptions.splice(randomIndex, 1);
              results.churnedCustomers++;
            }
          }

          // Simulate upgrades/downgrades (5-10% of active customers)
          const changeRate = 0.05 + Math.random() * 0.05;
          const changeCount = Math.floor(
            activeSubscriptions.length * changeRate
          );

          for (let i = 0; i < changeCount; i++) {
            if (activeSubscriptions.length > 0) {
              const randomIndex = Math.floor(
                Math.random() * activeSubscriptions.length
              );
              const subscriptionToModify = activeSubscriptions[randomIndex];
              const newPrice =
                prices[Math.floor(Math.random() * prices.length)];

              try {
                await stripe.subscriptions.update(subscriptionToModify.id, {
                  items: [
                    {
                      id: subscriptionToModify.items.data[0].id,
                      price: newPrice.id,
                    },
                  ],
                  proration_behavior: 'create_prorations',
                });

                // Determine if upgrade or downgrade based on price comparison
                const oldAmount =
                  subscriptionToModify.items.data[0].price.unit_amount || 0;
                const newAmount = newPrice.unit_amount || 0;

                if (newAmount > oldAmount) {
                  results.upgrades++;
                } else if (newAmount < oldAmount) {
                  results.downgrades++;
                }
              } catch (error) {
                console.log(`Failed to modify subscription: ${error}`);
              }
            }
          }

          // Simulate refunds (1-3% of active subscriptions)
          const refundRate = 0.01 + Math.random() * 0.02;
          const refundCount = Math.floor(
            activeSubscriptions.length * refundRate
          );

          for (let i = 0; i < refundCount; i++) {
            if (activeSubscriptions.length > 0) {
              try {
                // Get recent charges to refund
                const charges = await stripe.charges.list({
                  limit: 10,
                  created: { gte: monthStart - 30 * 24 * 60 * 60 },
                });

                if (charges.data.length > 0) {
                  const randomCharge =
                    charges.data[
                      Math.floor(Math.random() * charges.data.length)
                    ];

                  if (randomCharge.paid && !randomCharge.refunded) {
                    await stripe.refunds.create({
                      charge: randomCharge.id,
                      amount: Math.floor(
                        randomCharge.amount * (0.5 + Math.random() * 0.5)
                      ), // 50-100% refund
                    });
                    results.refunds++;
                  }
                }
              } catch (error) {
                console.log(`Failed to create refund: ${error}`);
              }
            }
          }

          // Advance to end of month and collect stats
          await stripe.testHelpers.testClocks.advance(testClockId, {
            frozen_time: monthEnd,
          });

          const monthStats = {
            month,
            newCustomers: newCustomersThisMonth,
            churnedCustomers: churnCount,
            activeSubscriptions: activeSubscriptions.length,
            totalCustomers: currentCustomerCount,
          };

          results.monthlyStats.push(monthStats);
        }

        return results;
      }
    );

    return {
      success: true,
      testClockId,
      initialCustomers: initialNumberOfCustomers,
      finalStats: simulationResults,
      products: Object.keys(products),
      pricesCreated: prices.length,
    };
  }
);

// Helper function to generate realistic customer data
function generateCustomerData(index: number) {
  const firstNames = [
    'John',
    'Jane',
    'Mike',
    'Sarah',
    'David',
    'Lisa',
    'Chris',
    'Emma',
    'Alex',
    'Maria',
  ];
  const lastNames = [
    'Smith',
    'Johnson',
    'Williams',
    'Brown',
    'Jones',
    'Garcia',
    'Miller',
    'Davis',
    'Rodriguez',
    'Martinez',
  ];
  const domains = [
    'gmail.com',
    'yahoo.com',
    'hotmail.com',
    'outlook.com',
    'company.com',
  ];

  const firstName = firstNames[index % firstNames.length];
  const lastName =
    lastNames[Math.floor(index / firstNames.length) % lastNames.length];
  const domain = domains[index % domains.length];

  return {
    name: `${firstName} ${lastName}`,
    email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}${index}@${domain}`,
  };
}
