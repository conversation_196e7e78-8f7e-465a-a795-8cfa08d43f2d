/**
 * Comprehensive Stripe Test Data Ingestion Script (v3)
 *
 * This script is designed to populate a Stripe test account with realistic,
 * time-based subscription data for backfilling and analytics testing.
 *
 * Key Features:
 * - SAFETY CHECKS: Prevents execution on live or production environments.
 * - Cleanup: Deletes existing test data to ensure a clean slate.
 * - Test Clocks: Uses Stripe Test Clocks to simulate the passage of time
 * for accurate event generation (invoices, subscriptions, webhooks).
 * - Historical Simulation: Starts the simulation at the test clock's
 * frozen time, allowing for the creation of historical data.
 * - Lifecycle Events: Simulates customer sign-ups, churn, upgrades,
 * downgrades, and refunds over time.
 * - Pagination: Correctly handles pagination when cleaning up existing
 * resources to ensure all data is removed.
 *
 * Usage:
 * - Ensure your environment variables are set: STRIPE_SECRET_KEY, etc.
 * - Run the script with an Inngest event: `stripe/populate.test.data`
 *
 * Example Event Data:
 * {
 * "testClockId": "clock_1Oq4p4RjW2XmC61y0Zk8j3zF",
 * "initialNumberOfCustomers": 50,
 * "orgIntegrationId": "your_integration_id"
 * }
 */

import Stripe from 'stripe';
import { inngest } from '../client';

interface IngestTestDataParams {
  initialNumberOfCustomers: number;
  orgIntegrationId: string;
}

export const populateStripeTestData = inngest.createFunction(
  { id: 'ingest-test-data' },
  { event: 'stripe/populate.test.data' },
  async ({ event }) => {
    const { initialNumberOfCustomers, orgIntegrationId } =
      event.data as IngestTestDataParams;

    // Get and validate integration
    // const integration = await getIntegrationById(orgIntegrationId);
    // if (!integration) {
    //   throw new Error(`Integration with ID ${orgIntegrationId} not found`);
    // }

    // SAFETY CHECK: Ensure this is NOT a live integration
    // if (integration.settings?.livemode === true) {
    //   throw new Error(
    //     'SAFETY ERROR: Cannot populate test data on a live Stripe integration!'
    //   );
    // }

    // if (integration.environment === 'production') {
    //   throw new Error(
    //     'SAFETY ERROR: Cannot populate test data on a production environment integration!'
    //   );
    // }

    // Initialize Stripe client
    // Use main org integration for now
    // const accessToken = getAccessTokenFromIntegration(integration);
    // if (!accessToken) {
    //   throw new Error('No access token found for integration');
    // }

    // process.env.STRIPE_SECRET_KEY
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-08-27.basil',
    });

    // --- Cleanup: Delete existing test data with pagination ---
    console.log('Cleaning up existing test data...');

    // Helper function for paginated deletion
    const deleteAllWithPagination = async (
      listFunction: any,
      deleteFunction: any
    ) => {
      let hasMore = true;
      let lastId: string | null = null;
      while (hasMore) {
        const listParams: any = {
          limit: 100,
          starting_after: lastId || undefined,
        };
        const listResult: any = await listFunction(listParams);

        if (listResult.data.length === 0) {
          hasMore = false;
          continue;
        }

        for (const item of listResult.data) {
          try {
            await deleteFunction(item.id);
          } catch (e: any) {
            console.warn(`Could not delete item ${item.id}: ${e.message}`);
          }
        }
        lastId = listResult.data[listResult.data.length - 1].id;
        hasMore = listResult.has_more;
      }
    };

    // Delete existing subscriptions
    await deleteAllWithPagination(
      (params: any) => stripe.subscriptions.list(params),
      (id: string) => stripe.subscriptions.cancel(id)
    );

    // Delete existing customers (we'll only have one, but clean up anyway)
    await deleteAllWithPagination(
      (params: any) => stripe.customers.list(params),
      (id: string) => stripe.customers.del(id)
    );

    // Delete existing products
    let hasMoreProducts = true;
    let lastProductId: string | null = null;
    while (hasMoreProducts) {
      const productsList: any = await stripe.products.list({
        limit: 100,
        starting_after: lastProductId || undefined,
      });
      for (const product of productsList.data) {
        await stripe.products.update(product.id, { active: false });
      }
      lastProductId = productsList.data[productsList.data.length - 1]?.id;
      hasMoreProducts = productsList.has_more;
    }

    // Delete existing test clocks
    await deleteAllWithPagination(
      (params: any) => stripe.testHelpers.testClocks.list(params),
      (id: string) => stripe.testHelpers.testClocks.del(id)
    );

    // --- Create initial test clock starting from January 1st, 2024 ---
    const jan1st2025 = Math.floor(
      new Date('2025-01-01T00:00:00Z').getTime() / 1000
    );
    const initialTestClock = await stripe.testHelpers.testClocks.create({
      frozen_time: jan1st2025,
      name: 'Initial Test Clock for Simulation - Starting Jan 1, 2024',
    });

    // --- Create products and prices ---
    console.log('Creating products and prices...');

    // Create Basic plan product
    const basicProduct = await stripe.products.create({
      name: 'Basic Plan',
      description: 'Basic SaaS plan with essential features',
    });

    // Create Pro plan product
    const proProduct = await stripe.products.create({
      name: 'Pro Plan',
      description: 'Professional SaaS plan with advanced features',
    });

    // Define pricing structure
    const pricingConfig = [
      // Basic plan prices
      { product: basicProduct.id, interval: 'week', amount: 999 }, // $9.99/week
      { product: basicProduct.id, interval: 'month', amount: 2999 }, // $29.99/month
      { product: basicProduct.id, interval: 'year', amount: 29999 }, // $299.99/year
      // Pro plan prices
      { product: proProduct.id, interval: 'week', amount: 1999 }, // $19.99/week
      { product: proProduct.id, interval: 'month', amount: 5999 }, // $59.99/month
      { product: proProduct.id, interval: 'year', amount: 59999 }, // $599.99/year
    ];

    const prices: any[] = [];
    for (const config of pricingConfig) {
      const price = await stripe.prices.create({
        product: config.product,
        unit_amount: config.amount,
        currency: 'usd',
        recurring: {
          interval: config.interval as 'week' | 'month' | 'year',
        },
      });
      prices.push(price);
    }

    const products = { basic: basicProduct, pro: proProduct };

    // --- Create individual test clocks and customers ---
    console.log('Creating individual test clocks and customers...');

    const customers = [];
    const subscriptions = [];
    const testClocks = [initialTestClock.id]; // Include the initial test clock

    // Create a test clock and customer for each initial customer
    for (let i = 0; i < initialNumberOfCustomers; i++) {
      const customerData = generateCustomerData(i);

      // Create a new test clock for this customer (except for the first one, use the initial one)
      let customerTestClockId = initialTestClock.id;
      if (i > 0) {
        const newTestClock = await stripe.testHelpers.testClocks.create({
          frozen_time: initialTestClock.frozen_time,
          name: `Test Clock for Customer ${i + 1}`,
        });
        customerTestClockId = newTestClock.id;
        testClocks.push(customerTestClockId);
      }

      const customer = await stripe.customers.create({
        email: customerData.email,
        name: customerData.name,
        description: `Test customer ${i + 1}`,
        test_clock: customerTestClockId,
      });

      // Create a dummy payment method using test token
      const paymentMethod = await stripe.paymentMethods.create({
        type: 'card',
        card: { token: 'tok_visa' }, // Use test token
      });

      // Attach the payment method to the customer
      await stripe.paymentMethods.attach(paymentMethod.id, {
        customer: customer.id,
      });

      // Set it as the default payment method
      await stripe.customers.update(customer.id, {
        invoice_settings: {
          default_payment_method: paymentMethod.id,
        },
      });

      const randomPrice = prices[Math.floor(Math.random() * prices.length)];

      const subscription = await stripe.subscriptions.create({
        customer: customer.id,
        items: [{ price: randomPrice.id }],
        collection_method: 'charge_automatically',
        default_payment_method: paymentMethod.id,
      });

      customers.push(customer);
      subscriptions.push(subscription);
    }

    const initialCustomers = { customers, subscriptions, testClocks };

    // --- Simulate business activity from test clock time to current time ---
    console.log('Simulating business activity...');
    const results = {
      weeklyStats: [] as Array<{
        week: number;
        startDate: Date;
        endDate: Date;
        newCustomers: number;
        churnedCustomers: number;
        activeSubscriptions: number;
        totalCustomers: number;
        upgrades: number;
        downgrades: number;
        refunds: number;
      }>,
      totalCustomers: initialNumberOfCustomers,
      totalRevenue: 0, // This would require a separate calculation or backfill script
      churnedCustomers: 0,
      upgrades: 0,
      downgrades: 0,
      refunds: 0,
    };

    let currentCustomerCount = initialNumberOfCustomers;
    let activeSubscriptions = [...initialCustomers.subscriptions];
    let allTestClocks = [...initialCustomers.testClocks];

    // Start from initial test clock's frozen time and simulate until current time
    const startTime = initialTestClock.frozen_time;
    const currentTime = Math.floor(Date.now() / 1000);
    const sevenDays = 7 * 24 * 60 * 60; // 7 days in seconds
    let simulationTime = startTime;
    let weekNumber = 1;

    while (simulationTime < currentTime) {
      const weekStart = simulationTime;
      const weekEnd = Math.min(simulationTime + sevenDays, currentTime);

      // Advance all test clocks to week start (with retry logic)
      console.log(
        `Advancing ${allTestClocks.length} test clocks to week ${weekNumber}...`
      );
      for (const clockId of allTestClocks) {
        await advanceTestClockWithRetry(stripe, clockId, weekStart);
      }

      // Calculate time-based growth rate
      const weeksFromStart = (weekStart - startTime) / sevenDays;
      const monthsFromStart = weeksFromStart / 4.33; // approximate weeks per month

      // Growth rate: 20-40% first 6 months, 10-20% after (converted to weekly)
      const monthlyGrowthRate =
        monthsFromStart <= 6
          ? 0.4 + Math.random() * 0.3 // between 0.4 and 0.7
          : 0.1 + Math.random() * 0.1; // between 0.1 and 0.2

      const weeklyGrowthRate = monthlyGrowthRate / 4.33;
      const newCustomersThisWeek = Math.floor(
        currentCustomerCount * weeklyGrowthRate
      );

      console.log(weeklyGrowthRate, 'JEBOTE');

      // Add new customers (each with their own test clock)
      const newCustomers = [];
      for (let i = 0; i < newCustomersThisWeek; i++) {
        const customerData = generateCustomerData(currentCustomerCount + i);

        // Create a new test clock for this customer
        const newTestClock = await stripe.testHelpers.testClocks.create({
          frozen_time: weekStart,
          name: `Test Clock for Week ${weekNumber} Customer ${i + 1}`,
        });
        allTestClocks.push(newTestClock.id);

        const customer = await stripe.customers.create({
          email: customerData.email,
          name: customerData.name,
          description: `Week ${weekNumber} customer`,
          test_clock: newTestClock.id,
        });

        // Create a dummy payment method using test token
        const paymentMethod = await stripe.paymentMethods.create({
          type: 'card',
          card: { token: 'tok_visa' }, // Use test token
        });

        // Attach the payment method to the customer
        await stripe.paymentMethods.attach(paymentMethod.id, {
          customer: customer.id,
        });

        // Set it as the default payment method
        await stripe.customers.update(customer.id, {
          invoice_settings: {
            default_payment_method: paymentMethod.id,
          },
        });

        const randomPrice = prices[Math.floor(Math.random() * prices.length)];
        const subscription = await stripe.subscriptions.create({
          customer: customer.id,
          items: [{ price: randomPrice.id }],
          collection_method: 'charge_automatically',
          default_payment_method: paymentMethod.id,
        });

        newCustomers.push({ customer, subscription });
        activeSubscriptions.push(subscription as any);
      }
      currentCustomerCount += newCustomersThisWeek;

      // Simulate churn (2-8% monthly, converted to weekly)
      const monthlyChurnRate = 0.02 + Math.random() * 0.06;
      const weeklyChurnRate = monthlyChurnRate / 4.33;
      const churnCount = Math.floor(
        activeSubscriptions.length * weeklyChurnRate
      );
      let weekChurnedCustomers = 0;
      for (let i = 0; i < churnCount; i++) {
        if (activeSubscriptions.length > 0) {
          const randomIndex = Math.floor(
            Math.random() * activeSubscriptions.length
          );
          const subscriptionToCancel = activeSubscriptions[randomIndex];
          await stripe.subscriptions.cancel(subscriptionToCancel.id);
          activeSubscriptions.splice(randomIndex, 1);
          results.churnedCustomers++;
          weekChurnedCustomers++;
        }
      }

      // Simulate upgrades/downgrades (5-10% monthly, converted to weekly)
      const monthlyChangeRate = 0.05 + Math.random() * 0.05;
      const weeklyChangeRate = monthlyChangeRate / 4.33;
      const changeCount = Math.floor(
        activeSubscriptions.length * weeklyChangeRate
      );
      let weekUpgrades = 0;
      let weekDowngrades = 0;
      for (let i = 0; i < changeCount; i++) {
        if (activeSubscriptions.length > 0) {
          const randomIndex = Math.floor(
            Math.random() * activeSubscriptions.length
          );
          const subscriptionToModify = activeSubscriptions[randomIndex];
          const oldPriceId = subscriptionToModify.items.data[0].price.id;
          const newPrice = prices.find(p => p.id !== oldPriceId); // find a different price
          if (!newPrice) continue;
          try {
            await stripe.subscriptions.update(subscriptionToModify.id, {
              items: [
                {
                  id: subscriptionToModify.items.data[0].id,
                  price: newPrice.id,
                },
              ],
              proration_behavior: 'create_prorations',
            });
            const oldAmount =
              subscriptionToModify.items.data[0].price.unit_amount || 0;
            const newAmount = newPrice.unit_amount || 0;
            if (newAmount > oldAmount) {
              results.upgrades++;
              weekUpgrades++;
            } else if (newAmount < oldAmount) {
              results.downgrades++;
              weekDowngrades++;
            }
          } catch (error) {
            console.log(`Failed to modify subscription: ${error}`);
          }
        }
      }

      // Simulate refunds (1-3% of active subscriptions monthly, converted to weekly)
      const monthlyRefundRate = 0.01 + Math.random() * 0.02;
      const weeklyRefundRate = monthlyRefundRate / 4.33;
      const refundCount = Math.floor(
        activeSubscriptions.length * weeklyRefundRate
      );
      let weekRefunds = 0;
      for (let i = 0; i < refundCount; i++) {
        if (activeSubscriptions.length > 0) {
          try {
            // Get charges from recent time period across all customers
            const charges = await stripe.charges.list({
              limit: 50,
              created: { gte: weekStart - 7 * 24 * 60 * 60 },
            });

            if (charges.data.length > 0) {
              const randomCharge =
                charges.data[Math.floor(Math.random() * charges.data.length)];
              if (randomCharge.paid && !randomCharge.refunded) {
                await stripe.refunds.create({
                  charge: randomCharge.id,
                  amount: Math.floor(
                    randomCharge.amount * (0.5 + Math.random() * 0.5)
                  ),
                  metadata: {
                    reason: 'Customer requested refund',
                    week: weekNumber.toString(),
                  },
                });
                results.refunds++;
                weekRefunds++;
              }
            }
          } catch (error) {
            console.log(`Failed to create refund: ${error}`);
          }
        }
      }

      // Advance all test clocks to end of week (with retry logic)
      console.log(
        `Advancing ${allTestClocks.length} test clocks to end of week ${weekNumber}...`
      );
      for (const clockId of allTestClocks) {
        await advanceTestClockWithRetry(stripe, clockId, weekEnd);
      }

      const weekStats = {
        week: weekNumber,
        startDate: new Date(weekStart * 1000),
        endDate: new Date(weekEnd * 1000),
        newCustomers: newCustomersThisWeek,
        churnedCustomers: weekChurnedCustomers,
        activeSubscriptions: activeSubscriptions.length,
        totalCustomers: currentCustomerCount,
        upgrades: weekUpgrades,
        downgrades: weekDowngrades,
        refunds: weekRefunds,
      };
      results.weeklyStats.push(weekStats);

      simulationTime = weekEnd;
      weekNumber++;
    }

    const simulationResults = results;

    return {
      success: true,
      initialTestClockId: initialTestClock.id,
      totalTestClocks: allTestClocks.length,
      initialCustomers: initialNumberOfCustomers,
      finalStats: simulationResults,
      products: Object.keys(products),
      pricesCreated: prices.length,
    };
  }
);

// Helper function to advance test clock with retry logic
async function advanceTestClockWithRetry(
  stripe: any,
  testClockId: string,
  targetTime: number,
  maxRetries: number = 10,
  delayMs: number = 2000
): Promise<void> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      await stripe.testHelpers.testClocks.advance(testClockId, {
        frozen_time: targetTime,
      });
      return; // Success, exit the function
    } catch (error: any) {
      if (error.message?.includes('Test clock advancement underway')) {
        console.log(
          `Test clock busy, attempt ${attempt}/${maxRetries}. Waiting ${delayMs}ms...`
        );
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, delayMs));
          continue;
        }
      }
      // If it's not a "busy" error or we've exhausted retries, throw the error
      throw error;
    }
  }
  throw new Error(`Failed to advance test clock after ${maxRetries} attempts`);
}

// Helper function to generate realistic customer data
function generateCustomerData(index: number) {
  const firstNames = [
    'John',
    'Jane',
    'Mike',
    'Sarah',
    'David',
    'Lisa',
    'Chris',
    'Emma',
    'Alex',
    'Maria',
  ];
  const lastNames = [
    'Smith',
    'Johnson',
    'Williams',
    'Brown',
    'Jones',
    'Garcia',
    'Miller',
    'Davis',
    'Rodriguez',
    'Martinez',
  ];
  const domains = [
    'gmail.com',
    'yahoo.com',
    'hotmail.com',
    'outlook.com',
    'company.com',
  ];

  const firstName = firstNames[index % firstNames.length];
  const lastName =
    lastNames[Math.floor(index / firstNames.length) % lastNames.length];
  const domain = domains[index % domains.length];

  return {
    name: `${firstName} ${lastName}`,
    email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}${index}@${domain}`,
  };
}
