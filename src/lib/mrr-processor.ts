import { db } from '@/db/client';
import { mrrLedgerCore } from '@/db/schema';
import { eq } from 'drizzle-orm';
import Stripe from 'stripe';
import { calculateMRREventsFromInvoices, MRREvent } from './mrr-calculator';
import { fetchSubscriptionWithInvoices } from './stripe';

export interface ProcessSubscriptionOptions {
  forceRecalculate?: boolean;
}

export interface ProcessSubscriptionResult {
  subscriptionId: string;
  eventsProcessed: number;
  eventsCreated: number;
  eventsUpdated: number;
  errors: string[];
}

/**
 * Process a single subscription and update the MRR ledger
 * This function is idempotent and can be safely re-run
 */
export async function processSubscription(
  stripe: Stripe,
  subscriptionId: string,
  options: ProcessSubscriptionOptions = {}
): Promise<ProcessSubscriptionResult> {
  const { forceRecalculate = false } = options;
  const result: ProcessSubscriptionResult = {
    subscriptionId,
    eventsProcessed: 0,
    eventsCreated: 0,
    eventsUpdated: 0,
    errors: [],
  };

  try {
    // Fetch subscription with complete invoice history for accurate MRR calculation
    const { subscription, invoices } = await fetchSubscriptionWithInvoices(
      stripe,
      subscriptionId
    );

    // Calculate MRR events from invoice history
    const mrrEvents = calculateMRREventsFromInvoices(subscription, invoices);

    result.eventsProcessed = mrrEvents.length;

    // Process each MRR event
    for (const mrrEvent of mrrEvents) {
      try {
        await upsertMRREvent(mrrEvent, forceRecalculate);

        // Check if this was an insert or update
        const existingEvent = await db
          .select()
          .from(mrrLedgerCore)
          .where(eq(mrrLedgerCore.uniqueSourceKey, mrrEvent.uniqueSourceKey))
          .limit(1);

        if (existingEvent.length > 0) {
          result.eventsUpdated++;
        } else {
          result.eventsCreated++;
        }
      } catch (error) {
        const errorMessage = `Failed to process MRR event ${mrrEvent.uniqueSourceKey}: ${error}`;
        result.errors.push(errorMessage);
        console.error(errorMessage);
      }
    }

    return result;
  } catch (error) {
    const errorMessage = `Failed to process subscription ${subscriptionId}: ${error}`;
    result.errors.push(errorMessage);
    console.error(errorMessage);
    return result;
  }
}

/**
 * Upsert an MRR event into the ledger
 * Uses ON CONFLICT to handle duplicates gracefully
 */
async function upsertMRREvent(
  mrrEvent: MRREvent,
  forceUpdate: boolean = false
): Promise<void> {
  const eventData = {
    uniqueSourceKey: mrrEvent.uniqueSourceKey,
    subscriptionId: mrrEvent.subscriptionId,
    srcEventType: mrrEvent.srcEventType,
    mrrDeltaCents: mrrEvent.mrrDeltaCents,
    effectiveDate: mrrEvent.effectiveDate,
    stripeEventId: mrrEvent.stripeEventId || null,
    customerId: mrrEvent.customerId,
    productId: mrrEvent.productId || null,
    priceId: mrrEvent.priceId || null,
    updatedAt: new Date(),
  };

  try {
    // Check if record exists
    const existing = await db
      .select()
      .from(mrrLedgerCore)
      .where(eq(mrrLedgerCore.uniqueSourceKey, mrrEvent.uniqueSourceKey))
      .limit(1);

    if (existing.length > 0) {
      // Update existing record if forceUpdate is true or if data has changed
      const existingRecord = existing[0];
      const hasChanges =
        existingRecord.mrrDelta !== mrrEvent.mrrDeltaCents ||
        existingRecord.effectiveDate !== mrrEvent.effectiveDate ||
        existingRecord.srcEventType !== mrrEvent.srcEventType ||
        existingRecord.customerId !== mrrEvent.customerId ||
        existingRecord.productId !== mrrEvent.productId ||
        existingRecord.priceId !== mrrEvent.priceId;

      if (forceUpdate || hasChanges) {
        await db
          .update(mrrLedgerCore)
          .set(eventData)
          .where(eq(mrrLedgerCore.uniqueSourceKey, mrrEvent.uniqueSourceKey));
      }
    } else {
      // Insert new record
      await db.insert(mrrLedgerCore).values({
        ...eventData,
        createdAt: new Date(),
      });
    }
  } catch (error) {
    console.error('Failed to upsert MRR event:', error);
    throw error;
  }
}

/**
 * Process multiple subscriptions in batches
 */
export async function processSubscriptionsBatch(
  stripe: Stripe,
  subscriptionIds: string[],
  options: ProcessSubscriptionOptions = {},
  batchSize: number = 10
): Promise<ProcessSubscriptionResult[]> {
  const results: ProcessSubscriptionResult[] = [];

  // Process in batches to avoid overwhelming the database
  for (let i = 0; i < subscriptionIds.length; i += batchSize) {
    const batch = subscriptionIds.slice(i, i + batchSize);

    const batchPromises = batch.map(subscriptionId =>
      processSubscription(stripe, subscriptionId, options)
    );

    const batchResults = await Promise.allSettled(batchPromises);

    for (const result of batchResults) {
      if (result.status === 'fulfilled') {
        results.push(result.value);
      } else {
        results.push({
          subscriptionId: 'unknown',
          eventsProcessed: 0,
          eventsCreated: 0,
          eventsUpdated: 0,
          errors: [result.reason?.message || 'Unknown error'],
        });
      }
    }

    // Small delay between batches to be nice to the database
    if (i + batchSize < subscriptionIds.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  return results;
}

/**
 * Get processing summary from results
 */
export function summarizeProcessingResults(
  results: ProcessSubscriptionResult[]
): {
  totalSubscriptions: number;
  totalEventsProcessed: number;
  totalEventsCreated: number;
  totalEventsUpdated: number;
  totalErrors: number;
  successRate: number;
} {
  const summary = results.reduce(
    (acc, result) => ({
      totalSubscriptions: acc.totalSubscriptions + 1,
      totalEventsProcessed: acc.totalEventsProcessed + result.eventsProcessed,
      totalEventsCreated: acc.totalEventsCreated + result.eventsCreated,
      totalEventsUpdated: acc.totalEventsUpdated + result.eventsUpdated,
      totalErrors: acc.totalErrors + result.errors.length,
    }),
    {
      totalSubscriptions: 0,
      totalEventsProcessed: 0,
      totalEventsCreated: 0,
      totalEventsUpdated: 0,
      totalErrors: 0,
    }
  );

  const successfulSubscriptions = results.filter(
    r => r.errors.length === 0
  ).length;
  const successRate =
    summary.totalSubscriptions > 0
      ? (successfulSubscriptions / summary.totalSubscriptions) * 100
      : 0;

  return {
    ...summary,
    successRate: Math.round(successRate * 100) / 100,
  };
}
