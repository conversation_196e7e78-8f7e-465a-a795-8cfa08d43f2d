// this is ingest route created using ingest serve

import { serve } from 'inngest/next';
import { inngest } from '../../../ingest/client';
import { populateStripeTestData } from '../../../ingest/stripe/populate-test-data';

// // Create an API that serves zero functions
export const { GET, POST, PUT } = serve({
  client: inngest,
  functions: [populateStripeTestData],
});

// export const dynamic = 'force-dynamic';
