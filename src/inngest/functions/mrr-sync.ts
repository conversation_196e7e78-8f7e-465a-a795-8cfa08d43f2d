import { processSubscription } from '@/lib/mrr-processor';
import { fetchAllSubscriptions, getStripeClient } from '@/lib/stripe';
import Stripe from 'stripe';
import { inngest } from '../client';

export interface MRRSyncPayload {
  orgId: string;
  subscriptionIds?: string[]; // If provided, only sync these specific subscriptions
  forceRecalculate?: boolean;
}

export interface MRRSyncResult {
  orgId: string;
  totalSubscriptions: number;
  totalEventsProcessed: number;
  totalEventsCreated: number;
  totalEventsUpdated: number;
  totalErrors: number;
  successRate: number;
  duration: number;
  errors: string[];
}

/**
 * Main MRR sync function that processes subscriptions in chunks of 100
 */
export const mrrSyncFunction = inngest.createFunction(
  {
    id: 'mrr-sync',
    name: 'Sync MRR Ledger from Stripe',
    concurrency: {
      limit: 5, // Limit concurrent executions to avoid overwhelming Stripe API
    },
    retries: 3,
  },
  { event: 'mrr/sync' },
  async ({ event, step, logger }) => {
    const startTime = Date.now();
    const payload = event.data as MRRSyncPayload;
    const { orgId, subscriptionIds, forceRecalculate = false } = payload;

    logger.info('Starting MRR sync', {
      orgId,
      subscriptionIds,
      forceRecalculate,
    });

    const stripe = await getStripeClient(orgId);
    if (!stripe) {
      throw new Error(`No Stripe integration found for org ${orgId}`);
    }

    // Get all subscription IDs first
    const allSubscriptionIds = await step.run(
      'get-all-subscription-ids',
      async () => {
        if (subscriptionIds && subscriptionIds.length > 0) {
          logger.info('Processing specific subscriptions', {
            count: subscriptionIds.length,
          });
          return subscriptionIds;
        }

        // Fetch all subscriptions
        logger.info('Fetching all subscriptions from Stripe');
        const allIds: string[] = [];

        for await (const subscription of fetchAllSubscriptions(stripe)) {
          allIds.push(subscription.id);
        }

        logger.info('Found subscriptions', {
          count: allIds.length,
        });
        return allIds;
      }
    );

    // Process subscriptions in chunks of 100 - one step per chunk
    const chunkSize = 100;
    const allResults: any[] = [];

    for (let i = 0; i < allSubscriptionIds.length; i += chunkSize) {
      const chunk = allSubscriptionIds.slice(i, i + chunkSize);
      const chunkIndex = Math.floor(i / chunkSize);

      const chunkResults = await step.run(
        `process-and-save-chunk-${chunkIndex}`,
        async () => {
          logger.info(`Processing and saving chunk ${chunkIndex + 1}`, {
            chunkSize: chunk.length,
            startIndex: i,
          });

          // This single step handles: fetch subs, fetch invoices, process MRR events, and save to DB
          return await processAndSaveSubscriptionChunk(stripe, chunk);
        }
      );

      allResults.push(...chunkResults);
    }

    // Step: Summarize results
    const summary = await step.run('summarize-results', async () => {
      const duration = Date.now() - startTime;
      const totalEvents = allResults.reduce(
        (sum, result) => sum + result.events.length,
        0
      );
      const totalErrors = allResults.reduce(
        (sum, result) => sum + result.errors.length,
        0
      );

      const result: MRRSyncResult = {
        orgId,
        totalSubscriptions: allSubscriptionIds.length,
        totalEventsProcessed: totalEvents,
        totalEventsCreated: totalEvents, // We don't track created vs updated anymore
        totalEventsUpdated: 0,
        totalErrors,
        successRate:
          totalErrors === 0
            ? 100
            : ((totalEvents - totalErrors) / totalEvents) * 100,
        duration,
        errors: allResults.flatMap(r => r.errors),
      };

      logger.info('MRR sync completed', result);
      return result;
    });

    return summary;
  }
);

/**
 * Process and save a chunk of subscriptions - handles everything in one step:
 * 1. Fetch subscription invoices using Stripe search
 * 2. Calculate MRR events from invoices
 * 3. Batch upsert all MRR events to database
 */
async function processAndSaveSubscriptionChunk(
  stripe: Stripe,
  subscriptionIds: string[]
): Promise<{
  results: Array<{ subscriptionId: string; events: any[]; errors: string[] }>;
  lastProcessedDate?: string;
  totalEvents: number;
}> {
  const results: Array<{
    subscriptionId: string;
    events: any[];
    errors: string[];
  }> = [];

  // Helper function to chunk arrays
  const chunk = <T>(arr: T[], n: number): T[][] =>
    arr.reduce(
      (a: T[][], _, i) => (i % n ? a : [...a, arr.slice(i, i + n)]),
      []
    );

  // All MRR events to batch upsert at the end
  const allMrrEvents: any[] = [];

  // Process subscriptions in groups of 10 for invoice fetching
  for (const group of chunk(subscriptionIds, 10)) {
    const query = group.map(s => `subscription:"${s}"`).join(' OR ');
    let page: string | undefined;

    do {
      try {
        const res = await stripe.invoices.search({
          query,
          limit: 100,
          page, // Stripe search uses 'page' tokens
          expand: ['data.subscription', 'data.customer'],
        });

        // Group invoices by subscription
        const invoicesBySubscription: Record<string, any[]> = {};
        for (const invoice of res.data) {
          const subId = (invoice as any).subscription;
          if (subId) {
            const subscriptionId = typeof subId === 'string' ? subId : subId.id;
            if (subscriptionId) {
              if (!invoicesBySubscription[subscriptionId]) {
                invoicesBySubscription[subscriptionId] = [];
              }
              invoicesBySubscription[subscriptionId].push(invoice);
            }
          }
        }

        // Process each subscription in this group
        for (const subscriptionId of group) {
          try {
            // Get subscription details
            const subscription = await stripe.subscriptions.retrieve(
              subscriptionId,
              {
                expand: ['items.price.product'],
              }
            );

            // Get invoices for this subscription
            const invoices = invoicesBySubscription[subscriptionId] || [];

            // Calculate MRR events (import the function we need)
            const { calculateMRREventsFromInvoices } = await import(
              '@/lib/mrr-calculator'
            );
            const mrrEvents = calculateMRREventsFromInvoices(
              subscription,
              invoices
            );

            // Add to batch for later upsert
            allMrrEvents.push(...mrrEvents);

            results.push({
              subscriptionId,
              events: mrrEvents,
              errors: [],
            });
          } catch (error) {
            results.push({
              subscriptionId,
              events: [],
              errors: [
                `Failed to process subscription ${subscriptionId}: ${error}`,
              ],
            });
          }
        }

        page = res.next_page || undefined;
      } catch (error) {
        // If search fails, fall back to processing individual subscriptions
        for (const subscriptionId of group) {
          try {
            const mrrEvents = await processSubscription(stripe, subscriptionId);
            results.push({
              subscriptionId,
              events: mrrEvents ? [mrrEvents] : [],
              errors: [],
            });
          } catch (error) {
            results.push({
              subscriptionId,
              events: [],
              errors: [
                `Failed to process subscription ${subscriptionId}: ${error}`,
              ],
            });
          }
        }
        break; // Exit the page loop since we processed all subscriptions
      }
    } while (page);
  }

  // Batch upsert all MRR events to database
  if (allMrrEvents.length > 0) {
    try {
      await batchUpsertMRREvents(allMrrEvents);
    } catch (error) {
      console.error('Failed to batch upsert MRR events:', error);
      // Add error to all results
      results.forEach(result => {
        result.errors.push(`Failed to save MRR events: ${error}`);
      });
    }
  }

  return results;
}

/**
 * Function to sync a single subscription (useful for webhook processing)
 */
export const mrrSyncSingleFunction = inngest.createFunction(
  {
    id: 'mrr-sync-single',
    name: 'Sync Single Subscription MRR',
    retries: 3,
  },
  { event: 'mrr/sync-single' },
  async ({ event, step, logger }) => {
    const payload = event.data as {
      orgId: string;
      subscriptionId: string;
      forceRecalculate?: boolean;
    };

    const { orgId, subscriptionId, forceRecalculate = false } = payload;

    logger.info('Starting single subscription MRR sync', {
      orgId,
      subscriptionId,
      forceRecalculate,
    });

    // Step 1: Get Stripe client
    const stripe = await getStripeClient(orgId);

    if (!stripe) {
      throw new Error(`No Stripe integration found for org ${orgId}`);
    }

    // Step 2: Process the subscription
    const result = await step.run('process-subscription', async () => {
      return await processSubscription(stripe, subscriptionId);
    });

    logger.info('Single subscription MRR sync completed', result);
    return result;
  }
);

/**
 * Function to handle Stripe webhook events and update MRR accordingly
 */
export const mrrWebhookFunction = inngest.createFunction(
  {
    id: 'mrr-webhook',
    name: 'Process Stripe Webhook for MRR',
    retries: 3,
  },
  { event: 'stripe/webhook' },
  async ({ event, step, logger }) => {
    const payload = event.data as {
      orgId: string;
      stripeEvent: any; // Stripe webhook event
    };

    const { orgId, stripeEvent } = payload;

    logger.info('Processing Stripe webhook for MRR', {
      orgId,
      eventType: stripeEvent.type,
      eventId: stripeEvent.id,
    });

    // Only process subscription-related events
    const relevantEventTypes = [
      'customer.subscription.created',
      'customer.subscription.updated',
      'customer.subscription.deleted',
      'invoice.payment_succeeded',
    ];

    if (!relevantEventTypes.includes(stripeEvent.type)) {
      logger.info('Skipping non-subscription event', {
        eventType: stripeEvent.type,
      });
      return { skipped: true, reason: 'Not a subscription event' };
    }

    // Extract subscription ID from the event
    const subscriptionId = await step.run(
      'extract-subscription-id',
      async () => {
        const eventData = stripeEvent.data.object;

        if (eventData.object === 'subscription') {
          return eventData.id;
        }

        if (eventData.object === 'invoice' && eventData.subscription) {
          return eventData.subscription;
        }

        throw new Error(
          `Could not extract subscription ID from event type ${stripeEvent.type}`
        );
      }
    );

    // Trigger single subscription sync
    const result = await step.run('sync-subscription', async () => {
      return await inngest.send({
        name: 'mrr/sync-single',
        data: {
          orgId,
          subscriptionId,
          forceRecalculate: true, // Force recalculation for webhook events
          includeHistory: false, // Don't need full history for webhook updates
        },
      });
    });

    logger.info('Webhook processing completed', { subscriptionId, result });
    return { subscriptionId, processed: true };
  }
);
