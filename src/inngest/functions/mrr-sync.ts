import {
  processSubscription,
  processSubscriptionsBatch,
  summarizeProcessingResults,
} from '@/lib/mrr-processor';
import { fetchAllSubscriptions, getStripeClient } from '@/lib/stripe';
import { inngest } from '../client';

export interface MRRSyncPayload {
  orgId: string;
  subscriptionIds?: string[]; // If provided, only sync these specific subscriptions
  forceRecalculate?: boolean;
}

export interface MRRSyncResult {
  orgId: string;
  totalSubscriptions: number;
  totalEventsProcessed: number;
  totalEventsCreated: number;
  totalEventsUpdated: number;
  totalErrors: number;
  successRate: number;
  duration: number;
  errors: string[];
}

/**
 * Main MRR sync function that processes all subscriptions for an organization
 */
export const mrrSyncFunction = inngest.createFunction(
  {
    id: 'mrr-sync',
    name: 'Sync MRR Ledger from Stripe',
    concurrency: {
      limit: 5, // Limit concurrent executions to avoid overwhelming Stripe API
    },
    retries: 3,
  },
  { event: 'mrr/sync' },
  async ({ event, step, logger }) => {
    const startTime = Date.now();
    const payload = event.data as MRRSyncPayload;
    const { orgId, subscriptionIds, forceRecalculate = false } = payload;

    logger.info('Starting MRR sync', {
      orgId,
      subscriptionIds,
      forceRecalculate,
    });

    const stripe = await getStripeClient(orgId);
    if (!stripe) {
      throw new Error(`No Stripe integration found for org ${orgId}`);
    }

    // Step 2: Get subscription IDs to process
    const subscriptionsToProcess = await step.run(
      'get-subscriptions',
      async () => {
        if (subscriptionIds && subscriptionIds.length > 0) {
          logger.info('Processing specific subscriptions', {
            count: subscriptionIds.length,
          });
          return subscriptionIds;
        }

        // Fetch all subscriptions
        logger.info('Fetching all subscriptions from Stripe');
        const allSubscriptionIds: string[] = [];

        for await (const subscription of fetchAllSubscriptions(stripe)) {
          allSubscriptionIds.push(subscription.id);
        }

        logger.info('Found subscriptions', {
          count: allSubscriptionIds.length,
        });
        return allSubscriptionIds;
      }
    );

    // Step 3: Process subscriptions in batches
    const results = await step.run('process-subscriptions', async () => {
      const batchSize = 20; // Process 20 subscriptions at a time
      const processingOptions = {
        forceRecalculate,
      };

      logger.info('Processing subscriptions in batches', {
        total: subscriptionsToProcess.length,
        batchSize,
      });

      return await processSubscriptionsBatch(
        stripe,
        subscriptionsToProcess,
        processingOptions,
        batchSize
      );
    });

    // Step 4: Summarize results
    const summary = await step.run('summarize-results', async () => {
      const summary = summarizeProcessingResults(results);
      const duration = Date.now() - startTime;

      const result: MRRSyncResult = {
        orgId,
        ...summary,
        duration,
        errors: results.flatMap(r => r.errors),
      };

      logger.info('MRR sync completed', result);
      return result;
    });

    return summary;
  }
);

/**
 * Function to sync a single subscription (useful for webhook processing)
 */
export const mrrSyncSingleFunction = inngest.createFunction(
  {
    id: 'mrr-sync-single',
    name: 'Sync Single Subscription MRR',
    retries: 3,
  },
  { event: 'mrr/sync-single' },
  async ({ event, step, logger }) => {
    const payload = event.data as {
      orgId: string;
      subscriptionId: string;
      forceRecalculate?: boolean;
    };

    const { orgId, subscriptionId, forceRecalculate = false } = payload;

    logger.info('Starting single subscription MRR sync', {
      orgId,
      subscriptionId,
      forceRecalculate,
    });

    // Step 1: Get Stripe client
    const stripe = await getStripeClient(orgId);

    if (!stripe) {
      throw new Error(`No Stripe integration found for org ${orgId}`);
    }

    // Step 2: Process the subscription
    const result = await step.run('process-subscription', async () => {
      return await processSubscription(stripe, subscriptionId, {
        forceRecalculate,
      });
    });

    logger.info('Single subscription MRR sync completed', result);
    return result;
  }
);

/**
 * Function to handle Stripe webhook events and update MRR accordingly
 */
export const mrrWebhookFunction = inngest.createFunction(
  {
    id: 'mrr-webhook',
    name: 'Process Stripe Webhook for MRR',
    retries: 3,
  },
  { event: 'stripe/webhook' },
  async ({ event, step, logger }) => {
    const payload = event.data as {
      orgId: string;
      stripeEvent: any; // Stripe webhook event
    };

    const { orgId, stripeEvent } = payload;

    logger.info('Processing Stripe webhook for MRR', {
      orgId,
      eventType: stripeEvent.type,
      eventId: stripeEvent.id,
    });

    // Only process subscription-related events
    const relevantEventTypes = [
      'customer.subscription.created',
      'customer.subscription.updated',
      'customer.subscription.deleted',
      'invoice.payment_succeeded',
    ];

    if (!relevantEventTypes.includes(stripeEvent.type)) {
      logger.info('Skipping non-subscription event', {
        eventType: stripeEvent.type,
      });
      return { skipped: true, reason: 'Not a subscription event' };
    }

    // Extract subscription ID from the event
    const subscriptionId = await step.run(
      'extract-subscription-id',
      async () => {
        const eventData = stripeEvent.data.object;

        if (eventData.object === 'subscription') {
          return eventData.id;
        }

        if (eventData.object === 'invoice' && eventData.subscription) {
          return eventData.subscription;
        }

        throw new Error(
          `Could not extract subscription ID from event type ${stripeEvent.type}`
        );
      }
    );

    // Trigger single subscription sync
    const result = await step.run('sync-subscription', async () => {
      return await inngest.send({
        name: 'mrr/sync-single',
        data: {
          orgId,
          subscriptionId,
          forceRecalculate: true, // Force recalculation for webhook events
          includeHistory: false, // Don't need full history for webhook updates
        },
      });
    });

    logger.info('Webhook processing completed', { subscriptionId, result });
    return { subscriptionId, processed: true };
  }
);
