import {
  bigint,
  date,
  index,
  jsonb,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';

export const integrationProviderEnum = pgEnum('integration_provider', [
  'stripe',
  //   "slack",
  //   "github",
  //   "notion",
  //   "custom",
]);

export const integrationStatusEnum = pgEnum('integration_status', [
  'pending',
  'connected',
  'error',
  'revoked',
]);

export const environmentEnum = pgEnum('environment', [
  'development',
  'production',
]);

export const orgIntegrations = pgTable(
  'org_integrations',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    orgId: varchar('org_id', { length: 128 }).notNull(),
    provider: integrationProviderEnum('provider').notNull(),
    environment: environmentEnum('environment').notNull().default('production'),
    // Provider-side account identifier (e.g., Stripe "acct_123")
    externalId: varchar('external_id', { length: 255 }),
    settings: jsonb('settings')
      .$type<Record<string, unknown>>()
      .default({})
      .notNull(),

    // Reference to secret in a secrets manager (e.g., "vercel:kv:my-secret-key")
    secretRef: text('secret_ref'),

    status: integrationStatusEnum('status').notNull().default('pending'),
    connectedAt: timestamp('connected_at'),
    revokedAt: timestamp('revoked_at'),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  t => ({
    // Prevent duplicates per org+provider+environment
    uniq: uniqueIndex('org_integrations_org_provider_env_uniq').on(
      t.orgId,
      t.provider,
      t.environment
    ),
  })
);

// MRR Event Types
export const mrrEventTypeEnum = pgEnum('mrr_event_type', [
  'start',
  'upgrade',
  'downgrade',
  'cancel',
]);

// MRR Ledger Core Table
export const mrrLedgerCore = pgTable(
  'mrr_ledger_core',
  {
    uniqueSourceKey: text('unique_source_key').primaryKey(), // sha256 hex of subscription_id + event_type + effective_date
    subscriptionId: text('subscription_id').notNull(), // Stripe subscription ID
    srcEventType: mrrEventTypeEnum('src_event_type').notNull(),
    mrrDeltaMinor: bigint('mrr_delta_minor', { mode: 'number' }).notNull(),
    effectiveDate: date('effective_date', { mode: 'string' }).notNull(), // 'YYYY-MM-DD'

    customerId: text('customer_id'), // Stripe customer ID
    productId: text('product_id'), // Stripe product ID
    priceId: text('price_id'), // Stripe price ID

    // Audit fields
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  t => ({
    // Index for efficient querying by subscription
    subscriptionIdx: index('mrr_ledger_subscription_idx').on(t.subscriptionId),
    // Index for efficient date range queries
    effectiveDateIdx: index('mrr_ledger_effective_date_idx').on(
      t.effectiveDate
    ),
    // Index for customer queries
    customerIdx: index('mrr_ledger_customer_idx').on(t.customerId),
  })
);
